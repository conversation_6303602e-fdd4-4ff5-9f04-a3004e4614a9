package main

import (
	"fmt"

	"regexp"
	"strings"
)

type studen struct {
	Sname      string
	Sage       int
	Scode      int
	Smaterials string
	class      string
	phone      string
}
type teacher struct {
	Tname            string
	Tage             int
	Tcode            int
	TeachingMaterial string
	Tclass           string
	phone            string
}
type clas struct {
	Cname   string
	studens []studen
	teacher teacher
}

func (s studen) printInfo() {
	fmt.Println("Student Name: ", s.Sname)
	StudenAge(s.Sage)
	StudenCode(s.Scode)
	studenclass(s.class)
	StudenMaterials(s.Smaterials)
	studenPhone(s.phone)
}

func (t teacher) printInfo() {
	fmt.Println("Teacher Name: ", t.Tname)
	Teacherage(t.Tage)
	TeacherCode(t.Tcode)
	TeacherMaterials(t.TeachingMaterial)
	teacherclass(t.Tclass)
	teacherPhone(t.phone)
}
func (c clas) printInfo() {
	fmt.Println("Class Name: ", c.Cname)
	fmt.Println("Teacher Name: ", c.teacher.Tname)
	fmt.Println("students this class:")
	for _, studen := range c.studens {
		studen.printInfo()

	}
}
func studenPhone(a string) bool {
	fmt.Println("Student phone: ", a)
	re := regexp.MustCompile(`^07\d{9}$`)
	return re.MatchString(a)
}
func StudenAge(a int) int {
	if a >= 7 && a <= 12 {
		fmt.Println("Student Age: ", a)
	} else {
		fmt.Println("error:age")
	}
	return a
}
func StudenCode(a int) int {
	if a >= 1000 && a <= 9999 {
		fmt.Println("Student Code: ", a)
	} else {
		fmt.Println("error:code")
	}
	return a
}
func StudenMaterials(s string) string {
	fmt.Println("student materials:")
	switch s {
	case "1":
		fmt.Println("1-reading\n", "2-mathematicd\n", "3-ethics\n", "4-english\n", "5-sciences\n", "6-islamic studies")
	case "2", "3":
		fmt.Println("1-reading\n", "2-mathematicd\n", "3-english\n", "4-sciences\n", "5-islamic studies")
	case "4", "5":
		fmt.Println("1-reading\n", "2-mathematicd\n", "3-english\n", "4-sciences\n", "5-islamic studies", "6-Social Studies\n", "7-Grammar")
	case "6":
		fmt.Println("1-reading\n", "2-mathematicd\n", "3-english\n", "4-sciences\n", "5-islamic studies", "6-Social Studies\n", "7-Grammar \n", "8-English Activity Book ")
	default:
		fmt.Println("error:no materials")
	}

	return s
}
func studenclass(q string) string {
	switch q {
	case "a", "b", "c", "d":
		fmt.Println("class:", q)
	}
	return q
}
func teacherclass(q string) string {

	switch q {
	case "a", "b", "c", "d":
		fmt.Println("class:", q)
	}
	return q
}
func TeacherMaterials(a string) string {
	TeacherMaterials := map[string]bool{

		"reading":               true,
		"mathematics":           true,
		"english":               true,
		"sciences":              true,
		"islamic Studies":       true,
		"social Studies":        true,
		"grammar":               true,
		"english Activity Book": true,
	}
	materials := strings.ToLower(a)
	if _, ok := TeacherMaterials[materials]; ok {
		fmt.Println("yes", a)
	} else {
		fmt.Println("error:no materials", a)
	}
	return a

}
func Teacherage(a int) int {
	if a >= 26 && a <= 63 {
		fmt.Println("yes", a)
	} else {
		fmt.Println("error: no age", a)
	}
	return a
}
func TeacherCode(a int) int {
	if a >= 1000 && a <= 9999 {
		fmt.Println("yes", a)
	} else {
		fmt.Println("error: no code", a)
	}
	return a
}
func teacherPhone(a string) bool {
	fmt.Println("teacher phone:", a)
	re := regexp.MustCompile(`^07\d{9}$`)
	return re.MatchString(a)
}

func main() {
	var choese string
	var slice_student []studen
	var slice_teachers []teacher
	var slice_class []clas
	for {
		choese = printi(choese)
		switch choese {
		case "1":
			var s studen
			fmt.Println("Enter the student name:")
			fmt.Scan(&s.Sname)
			fmt.Println("Enter the  student age:")
			fmt.Scan(&s.Sage)
			fmt.Println("Enter the student code(input 4 nambers):")
			fmt.Scan(&s.Scode)
			fmt.Println("Enter the student materials(1 to6 ):")
			fmt.Scan(&s.Smaterials)
			fmt.Println("Enter the student class(a,b,c,d):")
			fmt.Scan(&s.class)
			fmt.Println("Enter the student phone:")
			fmt.Scan(&s.phone)
			if !studenPhone(s.phone) {
				fmt.Println("Error: Invalid phone number")
				break
			}

			s.printInfo()
			slice_student = append(slice_student, s)
		case "2":
			var t teacher
			fmt.Println("Enter the Teacher name:")
			fmt.Scan(&t.Tname)
			fmt.Println("Enter the  Teacher age")
			fmt.Scan(&t.Tage)
			fmt.Println("Enter the teacher code(input 4 nambers)")
			fmt.Scan(&t.Tcode)
			fmt.Println("Enter the Teacher materials(reading\n", "mathematicd\n", "english\n", "sciences\n", "islamic studies", "Social Studies\n", "Grammar \n", "English Activity Book ")
			fmt.Scan(&t.TeachingMaterial)
			fmt.Println("Enter the class: ")
			fmt.Scan(&t.Tclass)
			fmt.Println("Enter the phone teacher: ")
			fmt.Scan(&t.phone)
			if !teacherPhone(t.phone) {
				fmt.Println("Error: Invalid phone number")
				break
			}
			t.printInfo()
			slice_teachers = append(slice_teachers, t)
		case "3":
			fmt.Println("all students ")
			for _, studen := range slice_student {
				studen.printInfo()
			}
		case "4":
			var classes clas
			fmt.Println("Enter the class:")
			fmt.Scan(&classes.Cname)
			classes.studens = slice_student
			slice_class = append(slice_class, classes)
		case "5":
			fmt.Println("all Clases")
			for _, clas := range slice_class {
				clas.printInfo()

			}
		case "6":

			fmt.Println("all Teachers")
			for _, teacher := range slice_teachers {
				teacher.printInfo()
			}
		case "7":
			fmt.Println("all students ")
			for _, studen := range slice_student {
				studen.printInfo()
			}
			fmt.Println("all Teachers")
			for _, teacher := range slice_teachers {
				teacher.printInfo()
			}
		case "8":
			fmt.Println("The Exiting")
			return
		default:
			fmt.Println("Invalid choice. Please try again")
		}
	}
}

func printi(choese string) string {
	fmt.Println("1-Information student")
	fmt.Println("2-Information teacher")
	fmt.Println("3-Information All students")
	fmt.Println("4-Information class")
	fmt.Println("5-all classes")
	fmt.Println("6-Information All teacher ")
	fmt.Println("7-Information All teachers and students ")
	fmt.Println("8-Exiting")
	fmt.Scan(&choese)
	return choese
}
